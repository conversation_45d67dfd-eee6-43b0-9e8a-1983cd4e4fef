'use client'

import { useState, useRef, useEffect } from 'react'

interface VideoThumbnailProps {
  videoUrl: string
  customThumbnailUrl?: string
  alt?: string
  className?: string
  timeInSeconds?: number
  showPlayButton?: boolean
  playButtonSize?: 'sm' | 'md' | 'lg'
}

export function VideoThumbnail({
  videoUrl,
  customThumbnailUrl,
  alt = 'Video thumbnail',
  className = '',
  timeInSeconds = 1,
  showPlayButton = true,
  playButtonSize = 'md'
}: VideoThumbnailProps) {
  const [showFallback, setShowFallback] = useState(false)
  const [videoLoaded, setVideoLoaded] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  const playButtonSizes = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  }

  const playIconSizes = {
    sm: 'w-3 h-3',
    md: 'w-5 h-5',
    lg: 'w-7 h-7'
  }

  // Handle video loading
  useEffect(() => {
    if (videoRef.current && !customThumbnailUrl) {
      const video = videoRef.current

      const handleLoadedData = () => {
        setVideoLoaded(true)
        // Seek to the desired time for preview
        video.currentTime = Math.min(timeInSeconds, video.duration - 0.1)
      }

      const handleError = () => {
        setShowFallback(true)
      }

      video.addEventListener('loadeddata', handleLoadedData)
      video.addEventListener('error', handleError)

      return () => {
        video.removeEventListener('loadeddata', handleLoadedData)
        video.removeEventListener('error', handleError)
      }
    }
  }, [videoUrl, timeInSeconds, customThumbnailUrl])

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleLoadedData = () => {
      setVideoLoaded(true)
    }

    const handleError = () => {
      setShowFallback(true)
    }

    // Set a timeout to show fallback if poster doesn't load quickly
    const fallbackTimer = setTimeout(() => {
      if (!thumbnailLoaded) {
        setShowFallback(true)
      }
    }, 2000)

    video.addEventListener('loadeddata', handleLoadedData)
    video.addEventListener('error', handleError)

    return () => {
      clearTimeout(fallbackTimer)
      video.removeEventListener('loadeddata', handleLoadedData)
      video.removeEventListener('error', handleError)
    }
  }, [videoUrl])

  // Fallback when video poster doesn't work - matches photo styling exactly
  if (showFallback) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full h-full bg-gray-200 flex items-center justify-center" style={{ aspectRatio: '1 / 1' }}>
          <div className="text-center">
            <div className="text-4xl mb-2 opacity-60">🎥</div>
            <div className="text-xs text-gray-600 font-medium">Video Preview</div>
          </div>
        </div>
        {showPlayButton && (
          <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
            <div className={`${playButtonSizes[playButtonSize]} bg-white/95 rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors`}>
              <svg className={`${playIconSizes[playButtonSize]} text-gray-800 ml-0.5`} fill="currentColor" viewBox="0 0 20 20">
                <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
              </svg>
            </div>
          </div>
        )}
        <div className="absolute top-2 left-2">
          <span className="bg-red-500/90 text-white text-xs px-1.5 py-0.5 rounded font-medium">
            VIDEO
          </span>
        </div>
      </div>
    )
  }

  // Use custom thumbnail if available
  if (customThumbnailUrl) {
    return (
      <div className={`relative ${className}`}>
        <img
          src={customThumbnailUrl}
          alt={alt}
          className="w-full h-full object-cover"
          style={{ aspectRatio: '1 / 1' }}
        />
        {showPlayButton && (
          <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
            <div className={`${playButtonSizes[playButtonSize]} bg-white/95 rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors`}>
              <svg className={`${playIconSizes[playButtonSize]} text-gray-800 ml-0.5`} fill="currentColor" viewBox="0 0 20 20">
                <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
              </svg>
            </div>
          </div>
        )}
        <div className="absolute top-2 left-2">
          <span className="bg-red-500/90 text-white text-xs px-1.5 py-0.5 rounded font-medium">
            VIDEO
          </span>
        </div>
      </div>
    )
  }

  // Show video preview (paused at specific time)
  return (
    <div className={`relative ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        style={{ aspectRatio: '1 / 1' }}
        preload="metadata"
        muted
        playsInline
        onLoadedData={() => setVideoLoaded(true)}
        onError={() => setShowFallback(true)}
      >
        <source src={videoUrl} type="video/mp4" />
      </video>

      {/* Loading state */}
      {!videoLoaded && !showFallback && (
        <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-2 border-gray-300 border-t-blue-600 mx-auto mb-1"></div>
            <div className="text-xs text-gray-600">Loading...</div>
          </div>
        </div>
      )}

      {/* Fallback state */}
      {showFallback && (
        <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
          <div className="text-center">
            <svg className="w-8 h-8 text-gray-400 mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
            </svg>
            <div className="text-xs text-gray-600">Video</div>
          </div>
        </div>
      )}

      {/* Play button overlay */}
      {showPlayButton && videoLoaded && (
        <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
          <div className={`${playButtonSizes[playButtonSize]} bg-white/95 rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors`}>
            <svg className={`${playIconSizes[playButtonSize]} text-gray-800 ml-0.5`} fill="currentColor" viewBox="0 0 20 20">
              <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
            </svg>
          </div>
        </div>
      )}

      {/* Video badge */}
      <div className="absolute top-2 left-2">
        <span className="bg-red-500/90 text-white text-xs px-1.5 py-0.5 rounded font-medium">
          VIDEO
        </span>
      </div>
    </div>
  )
}
