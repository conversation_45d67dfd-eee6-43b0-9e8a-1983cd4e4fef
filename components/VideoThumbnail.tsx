'use client'

import { useState, useRef, useEffect } from 'react'
import { generateVideoThumbnailWithAspectRatio } from '@/lib/video-thumbnail'

interface VideoThumbnailProps {
  videoUrl: string
  customThumbnailUrl?: string
  alt?: string
  className?: string
  timeInSeconds?: number
  showPlayButton?: boolean
  playButtonSize?: 'sm' | 'md' | 'lg'
}

export function VideoThumbnail({
  videoUrl,
  customThumbnailUrl,
  alt = 'Video thumbnail',
  className = '',
  timeInSeconds = 1,
  showPlayButton = true,
  playButtonSize = 'md'
}: VideoThumbnailProps) {
  const [thumbnailLoaded, setThumbnailLoaded] = useState(false)
  const [showFallback, setShowFallback] = useState(false)
  const [generatedThumbnail, setGeneratedThumbnail] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  const playButtonSizes = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  }

  const playIconSizes = {
    sm: 'w-3 h-3',
    md: 'w-5 h-5',
    lg: 'w-7 h-7'
  }

  // Generate thumbnail from video if no custom thumbnail is provided
  useEffect(() => {
    if (!customThumbnailUrl && !generatedThumbnail && !isGenerating && videoUrl) {
      setIsGenerating(true)
      generateVideoThumbnailWithAspectRatio(videoUrl, 1, timeInSeconds)
        .then((thumbnailDataUrl) => {
          setGeneratedThumbnail(thumbnailDataUrl)
          setThumbnailLoaded(true)
        })
        .catch((error) => {
          console.error('Failed to generate video thumbnail:', error)
          setShowFallback(true)
        })
        .finally(() => {
          setIsGenerating(false)
        })
    }
  }, [videoUrl, customThumbnailUrl, timeInSeconds, generatedThumbnail, isGenerating])

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleLoadedData = () => {
      setThumbnailLoaded(true)
    }

    const handleError = () => {
      setShowFallback(true)
    }

    // Set a timeout to show fallback if poster doesn't load quickly
    const fallbackTimer = setTimeout(() => {
      if (!thumbnailLoaded) {
        setShowFallback(true)
      }
    }, 2000)

    video.addEventListener('loadeddata', handleLoadedData)
    video.addEventListener('error', handleError)

    return () => {
      clearTimeout(fallbackTimer)
      video.removeEventListener('loadeddata', handleLoadedData)
      video.removeEventListener('error', handleError)
    }
  }, [thumbnailLoaded])

  // Fallback when video poster doesn't work - matches photo styling exactly
  if (showFallback) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full h-full bg-gray-200 flex items-center justify-center" style={{ aspectRatio: '1 / 1' }}>
          <div className="text-center">
            <div className="text-4xl mb-2 opacity-60">🎥</div>
            <div className="text-xs text-gray-600 font-medium">Video Preview</div>
          </div>
        </div>
        {showPlayButton && (
          <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
            <div className={`${playButtonSizes[playButtonSize]} bg-white/95 rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors`}>
              <svg className={`${playIconSizes[playButtonSize]} text-gray-800 ml-0.5`} fill="currentColor" viewBox="0 0 20 20">
                <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
              </svg>
            </div>
          </div>
        )}
        <div className="absolute top-2 left-2">
          <span className="bg-red-500/90 text-white text-xs px-1.5 py-0.5 rounded font-medium">
            VIDEO
          </span>
        </div>
      </div>
    )
  }

  // Use custom thumbnail or generated thumbnail if available
  if (customThumbnailUrl || generatedThumbnail) {
    return (
      <div className={`relative ${className}`}>
        <img
          src={customThumbnailUrl || generatedThumbnail || ''}
          alt={alt}
          className="w-full h-full object-cover"
          style={{ aspectRatio: '1 / 1' }}
        />
        {showPlayButton && (
          <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
            <div className={`${playButtonSizes[playButtonSize]} bg-white/95 rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors`}>
              <svg className={`${playIconSizes[playButtonSize]} text-gray-800 ml-0.5`} fill="currentColor" viewBox="0 0 20 20">
                <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
              </svg>
            </div>
          </div>
        )}
        <div className="absolute top-2 left-2">
          <span className="bg-red-500/90 text-white text-xs px-1.5 py-0.5 rounded font-medium">
            VIDEO
          </span>
        </div>
      </div>
    )
  }

  // Show loading state while generating thumbnail
  if (isGenerating) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full h-full bg-gray-200 flex items-center justify-center" style={{ aspectRatio: '1 / 1' }}>
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-blue-600 mx-auto mb-2"></div>
            <div className="text-xs text-gray-600 font-medium">Loading...</div>
          </div>
        </div>
        <div className="absolute top-2 left-2">
          <span className="bg-red-500/90 text-white text-xs px-1.5 py-0.5 rounded font-medium">
            VIDEO
          </span>
        </div>
      </div>
    )
  }

  // Final fallback: Try to use video element with poster, but also attempt thumbnail generation
  useEffect(() => {
    if (!generatedThumbnail && !isGenerating && !customThumbnailUrl && videoRef.current) {
      const video = videoRef.current
      const handleLoadedMetadata = () => {
        // Try to generate thumbnail as fallback
        if (!generatedThumbnail) {
          setIsGenerating(true)
          generateVideoThumbnailWithAspectRatio(videoUrl, 1, timeInSeconds)
            .then(setGeneratedThumbnail)
            .catch(() => setShowFallback(true))
            .finally(() => setIsGenerating(false))
        }
      }

      video.addEventListener('loadedmetadata', handleLoadedMetadata)
      return () => video.removeEventListener('loadedmetadata', handleLoadedMetadata)
    }
  }, [videoUrl, timeInSeconds, generatedThumbnail, isGenerating, customThumbnailUrl])

  return (
    <div className={`relative ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        style={{ aspectRatio: '1 / 1' }}
        preload="metadata"
        muted
        poster={`${videoUrl}#t=${timeInSeconds}`}
      >
        <source src={videoUrl} type="video/mp4" />
      </video>
      {showPlayButton && (
        <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
          <div className={`${playButtonSizes[playButtonSize]} bg-white/95 rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors`}>
            <svg className={`${playIconSizes[playButtonSize]} text-gray-800 ml-0.5`} fill="currentColor" viewBox="0 0 20 20">
              <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
            </svg>
          </div>
        </div>
      )}
      <div className="absolute top-2 left-2">
        <span className="bg-red-500/90 text-white text-xs px-1.5 py-0.5 rounded font-medium">
          VIDEO
        </span>
      </div>
    </div>
  )
}
