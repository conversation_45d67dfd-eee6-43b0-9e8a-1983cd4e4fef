'use client'

import React, { useState, useEffect } from 'react'
import { AudioPost } from './AudioPost'
import { PaywallContent } from './PaywallContent'
import { Day1Badge } from './Day1Badge'
import Image from 'next/image'
import Link from 'next/link'

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  created_at: string
  is_free: boolean
  bundle_count: number
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
  love_count: number
  view_count: number
  photos?: Array<{ id: string; url: string; alt_text: string }>
  videos?: Array<{ id: string; r2_public_url: string; title?: string; view_count?: number; custom_thumbnail_url?: string }>
  type: 'diary'
  isFollowing: boolean
  isSubscribed?: boolean
}

interface AudioPostType {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  love_count: number
  reply_count: number
  created_at: string
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
  type: 'audio'
  isFollowing: boolean
  isSubscribed?: boolean
}

interface BookReleaseType {
  id: string
  title: string
  description: string
  cover_image_url?: string
  genre?: string
  price_amount: number
  average_rating?: number
  review_count?: number
  sales_count?: number
  created_at: string
  author_name?: string
  is_ebook: boolean
  slug?: string
  user_id: string
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
  type: 'book'
  isFollowing: boolean
  isSubscribed?: boolean
}

type TimelinePost = DiaryEntry | AudioPostType | BookReleaseType

interface UnifiedTimelineProps {
  currentUserId?: string
  onUserClick?: (userId: string) => void
}

export function UnifiedTimeline({ currentUserId, onUserClick }: UnifiedTimelineProps) {
  const [posts, setPosts] = useState<TimelinePost[]>([])
  const [loading, setLoading] = useState(true)
  const [hasMore, setHasMore] = useState(true)

  useEffect(() => {
    loadPosts()
  }, [])

  const loadPosts = async (offset = 0) => {
    try {
      const response = await fetch(`/api/timeline?limit=20&offset=${offset}`)
      const data = await response.json()
      
      if (offset === 0) {
        setPosts(data.posts || [])
      } else {
        setPosts(prev => [...prev, ...(data.posts || [])])
      }
      
      setHasMore(data.hasMore || false)
    } catch (error) {
      console.error('Error loading timeline:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAudioLove = async (postId: string) => {
    try {
      const response = await fetch(`/api/audio/posts/${postId}/love`, {
        method: 'POST'
      })
      
      if (response.ok) {
        const { loved } = await response.json()

        setPosts(prev => prev.map(post => {
          if (post.type === 'audio' && post.id === postId) {
            return {
              ...post,
              love_count: loved ? post.love_count + 1 : post.love_count - 1
            }
          }
          return post
        }))
      }
    } catch (error) {
      console.error('Error toggling audio love:', error)
    }
  }

  const handleAudioReply = (postId: string) => {
    // TODO: Implement audio reply functionality
    console.log('Audio reply for post:', postId)
  }

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const postDate = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'now'
    if (diffInMinutes < 60) return `${diffInMinutes}m`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`
    return `${Math.floor(diffInMinutes / 1440)}d`
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 animate-pulse">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-32 mb-2"></div>
                <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-20"></div>
              </div>
            </div>
            <div className="h-20 bg-gradient-to-r from-gray-200 to-gray-300 rounded mb-4"></div>
            <div className="flex gap-4">
              <div className="h-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-16"></div>
              <div className="h-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-16"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
        {posts.map((post) => {
        if (post.type === 'audio') {
          return (
            <AudioPost
              key={`audio-${post.id}`}
              post={post}
              currentUserId={currentUserId}
              isFollowing={post.isFollowing}

              onLove={handleAudioLove}
              onReply={handleAudioReply}
              onUserClick={onUserClick}
            />
          )
        } else if (post.type === 'book') {
          // Book release
          return (
            <div key={`book-${post.id}`} className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg hover:border-blue-200 transition-all duration-300 hover:bg-gradient-to-br hover:from-white hover:to-blue-50/20">
              {/* Header */}
              <div className="flex items-start gap-3 mb-3">
                <button
                  onClick={() => onUserClick?.(post.user.id)}
                  className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0 hover:ring-2 hover:ring-gray-300 transition-all cursor-pointer"
                >
                  {post.user.avatar || post.user.profile_picture_url ? (
                    <Image
                      src={(post.user.avatar || post.user.profile_picture_url) as string}
                      alt={post.user.name || 'User avatar'}
                      width={48}
                      height={48}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-gray-500 text-lg">👤</span>
                  )}
                </button>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => onUserClick?.(post.user.id)}
                        className="font-semibold text-gray-900 hover:text-blue-600 transition-colors truncate"
                      >
                        {post.user.name}
                      </button>
                      {post.user.has_day1_badge && (
                        <Day1Badge
                          signupNumber={post.user.signup_number}
                          badgeTier={post.user.badge_tier}
                          size="sm"
                          className="flex-shrink-0"
                        />
                      )}
                    </div>
                    <span className="text-gray-400 text-sm">•</span>
                    <span className="text-gray-500 text-sm">
                      {formatTimeAgo(post.created_at)}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span className="text-blue-500">📚</span>
                    <span>Published a Book</span>
                    {post.isSubscribed && (
                      <div className="flex items-center gap-1 bg-purple-50 px-2 py-0.5 rounded-full">
                        <span className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse"></span>
                        <span className="text-purple-700 uppercase tracking-wide text-[10px] font-medium">SUBSCRIBER</span>
                      </div>
                    )}
                    {!post.isSubscribed && post.isFollowing && (
                      <div className="flex items-center gap-1 bg-green-50 px-2 py-0.5 rounded-full">
                        <span className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></span>
                        <span className="text-green-700 uppercase tracking-wide text-[10px] font-medium">FOLLOWING</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Book Content */}
              <Link href={`/books/${post.slug || post.id}`} className="block">
                <div className="flex gap-4">
                  {/* Book Cover */}
                  <div className="w-20 h-28 bg-gradient-to-br from-blue-100 to-purple-100 rounded flex-shrink-0 overflow-hidden">
                    {post.cover_image_url ? (
                      <Image
                        src={post.cover_image_url}
                        alt={post.title}
                        width={80}
                        height={112}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <span className="text-2xl opacity-50">📖</span>
                      </div>
                    )}
                  </div>

                  {/* Book Details */}
                  <div className="flex-1 min-w-0">
                    <h2 className="text-lg font-serif text-gray-900 mb-2 leading-tight line-clamp-2">
                      {post.title}
                    </h2>
                    <p className="text-gray-600 text-sm line-clamp-3 mb-3">
                      {post.description}
                    </p>

                    {/* Book Metadata */}
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      {post.genre && (
                        <span className="bg-gray-100 px-2 py-1 rounded text-xs">
                          {post.genre}
                        </span>
                      )}
                      <span className="font-medium text-green-600">
                        {post.price_amount === 0 ? 'Free' : `$${(post.price_amount / 100).toFixed(2)}`}
                      </span>
                      {post.average_rating && post.review_count && post.review_count > 0 && (
                        <div className="flex items-center gap-1">
                          <span className="text-yellow-400">⭐</span>
                          <span>{post.average_rating.toFixed(1)}</span>
                          <span>({post.review_count})</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Link>

              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-3 text-gray-500">
                    <span className="text-xl">📖</span>
                    <span className="text-sm font-medium">{post.sales_count || 0} sales</span>
                  </div>
                  {post.average_rating && (
                    <div className="flex items-center gap-3 text-gray-500">
                      <span className="text-xl">⭐</span>
                      <span className="text-sm font-medium">{post.average_rating.toFixed(1)} rating</span>
                    </div>
                  )}
                </div>

                {/* Subtle floating indicator */}
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
                  <div className="w-1 h-1 bg-purple-300 rounded-full animate-pulse delay-100"></div>
                  <div className="w-1.5 h-1.5 bg-pink-300 rounded-full animate-pulse delay-200"></div>
                </div>
              </div>
            </div>
          )
        } else {
          // Diary entry
          return (
            <div key={`diary-${post.id}`} className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg hover:border-purple-200 transition-all duration-300 hover:bg-gradient-to-br hover:from-white hover:to-purple-50/20">
              {/* Header */}
              <div className="flex items-start gap-3 mb-3">
                <button
                  onClick={() => onUserClick?.(post.user.id)}
                  className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0 hover:ring-2 hover:ring-gray-300 transition-all cursor-pointer"
                >
                  {post.user.avatar || post.user.profile_picture_url ? (
                    <Image
                      src={(post.user.avatar || post.user.profile_picture_url) as string}
                      alt={post.user.name || 'User avatar'}
                      width={48}
                      height={48}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-gray-500 text-lg">👤</span>
                  )}
                </button>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => onUserClick?.(post.user.id)}
                        className="font-semibold text-gray-900 hover:text-blue-600 transition-colors truncate"
                      >
                        {post.user.name}
                      </button>
                      {post.user.has_day1_badge && (
                        <Day1Badge
                          signupNumber={post.user.signup_number}
                          badgeTier={post.user.badge_tier}
                          size="sm"
                          className="flex-shrink-0"
                        />
                      )}
                    </div>
                    <span className="text-gray-400 text-sm">•</span>
                    <span className="text-gray-500 text-sm">
                      {formatTimeAgo(post.created_at)}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span className="text-purple-500">📔</span>
                    <span>Diary Entry</span>
                    {post.isSubscribed && (
                      <div className="flex items-center gap-1 bg-purple-50 px-2 py-0.5 rounded-full">
                        <span className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse"></span>
                        <span className="text-purple-700 uppercase tracking-wide text-[10px] font-medium">SUBSCRIBER</span>
                      </div>
                    )}
                    {!post.isSubscribed && post.isFollowing && (
                      <div className="flex items-center gap-1 bg-green-50 px-2 py-0.5 rounded-full">
                        <span className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></span>
                        <span className="text-green-700 uppercase tracking-wide text-[10px] font-medium">FOLLOWING</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Title */}
              <h2 className="text-lg font-serif text-gray-900 mb-2 leading-tight">
                {post.title}
              </h2>

              {/* Content */}
              <PaywallContent
                content={post.body_md}
                hasAccess={post.has_access}
                isFree={post.is_free}
                photos={post.photos}
                videos={post.videos}
                writerName={post.user.name}
                writerId={post.user.id}
                entryId={post.id}
                showVideoThumbnails={true}
              />

              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center gap-6">
                  <button className="group flex items-center gap-3 text-gray-600 hover:text-red-500 transition-all duration-300 hover:scale-105">
                    <div className="relative">
                      <span className="text-xl group-hover:animate-pulse">❤️</span>
                      <div className="absolute -inset-2 bg-red-100/50 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 -z-10 blur-sm"></div>
                    </div>
                    <span className="text-sm font-bold bg-gradient-to-r from-red-500 to-pink-500 bg-clip-text text-transparent group-hover:from-red-600 group-hover:to-pink-600">{post.love_count}</span>
                  </button>
                  <div className="flex items-center gap-3 text-gray-500 hover:text-gray-700 transition-colors duration-300">
                    <span className="text-xl">👁️</span>
                    <span className="text-sm font-medium">{post.view_count}</span>
                  </div>
                </div>

                {/* Subtle floating indicator */}
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse"></div>
                  <div className="w-1 h-1 bg-pink-300 rounded-full animate-pulse delay-100"></div>
                  <div className="w-1.5 h-1.5 bg-blue-300 rounded-full animate-pulse delay-200"></div>
                </div>
              </div>
            </div>
          )
        }
      })}

      {hasMore && (
        <div className="text-center py-8">
          <button
            onClick={() => loadPosts(posts.length)}
            className="px-6 py-3 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium text-gray-700 transition-colors"
          >
            Load More
          </button>
        </div>
      )}

      {posts.length === 0 && !loading && (
        <div className="text-center py-12">
          <p className="text-gray-500 font-serif text-lg mb-4">
            No posts yet. Start creating content!
          </p>
          <Link
            href="/write"
            className="inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
          >
            Create Your First Post
          </Link>
        </div>
      )}
    </div>
  )
}
